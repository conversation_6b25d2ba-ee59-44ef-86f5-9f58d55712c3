<template>
  <div class="flex-1 bg-[#15274D]/40 backdrop-blur-sm rounded overflow-hidden relative">
    <!-- 标题栏 -->
    <div class="h-[1.6vw] shrink-0 relative flex items-center">
      <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
      <div class="text-white absolute left-[1vw] top-1/2 -translate-y-1/2 text-[0.8vw] font-medium tracking-wider">实时报警监测</div>
      <div
        class="absolute right-[1vw] top-1/2 -translate-y-1/2 text-[0.6vw] text-blue-400 cursor-pointer hover:text-blue-300 transition-colors"
        @click="showSmartFireSafety"
        >智慧安消防</div
      >
    </div>

    <!-- 内容区域 -->
    <div class="h-[calc(100%-1.6vw)] p-[0.8vw]">
      <!-- 报警系统连接状态 -->
      <div class="flex items-center text-[0.7vw] justify-end">
        <div class="w-[0.5vw] h-[0.5vw] bg-red-500 rounded-full mr-[0.4vw] animate-[pulse_1.5s_ease-in-out_infinite]"></div>
        <span class="text-red-400">已连接报警系统</span>
      </div>

      <!-- 统计卡片组 -->
      <div class="grid grid-cols-4 gap-[0.6vw] mb-[0.8vw]">
        <div v-for="(stat, index) in alarmStats" :key="index" class="bg-[#1E2A47]/50 rounded p-[0.6vw] border-l-2" :class="stat.borderColor">
          <div class="text-[0.65vw] text-white/70 mb-[0.2vw]">{{ stat.label }}</div>
          <div class="flex items-end">
            <span class="text-[1.1vw] font-medium" :class="stat.textColor">
              <span>{{ stat.value }}</span>
            </span>
            <span class="text-[0.6vw] ml-[0.3vw] text-white">{{ stat.unit }}</span>
          </div>
        </div>
      </div>

      <!-- 报警列表区域 -->
      <div class="h-[calc(100%-5vw)] bg-[#0C1526]/50 rounded overflow-hidden">
        <!-- 表头 -->
        <div class="grid grid-cols-24 text-[0.65vw] bg-[#1E2A47] text-white/80 p-[0.5vw]">
          <div class="col-span-3 text-center">级别</div>
          <div class="col-span-10 text-center">位置</div>
          <div class="col-span-6 text-center">类型</div>
          <div class="col-span-4 text-center">时间</div>
        </div>
        <!-- 列表区域 -->
        <div ref="scrollContainer" class="overflow-hidden h-[calc(100%-1.7vw)]">
          <div ref="scrollContent" class="w-full">
            <div
              v-for="item in alarmList"
              :key="item.id"
              class="grid grid-cols-24 text-[0.65vw] text-white/80 items-center border-b border-white/10 p-[0.1vw] hover:bg-white/10 cursor-pointer transition-colors"
              @click="handleAlarm(item)"
            >
              <div class="col-span-3 flex justify-center">
                <MessageTip placement="top" :content="getAlarmLevelText(item.level)">
                  <div class="w-[0.6vw] h-[0.6vw] rounded-full" :class="getAlarmLevelClass(item.level)"></div>
                </MessageTip>
              </div>
              <div class="col-span-10">
                <MessageTip placement="top" :content="item.location">
                  <div class="truncate text-center">{{ item.location }}</div>
                </MessageTip>
              </div>
              <div class="col-span-6">
                <MessageTip placement="top" :content="item.type">
                  <div class="truncate text-center">{{ item.type }}</div>
                </MessageTip>
              </div>
              <div class="col-span-4">
                <MessageTip placement="top" :content="item.time">
                  <div class="truncate text-center">{{ formatTime(item.time) }}</div>
                </MessageTip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 报警处理弹窗 -->
  <ModalDialog
    v-model:visible="showAlarmModal"
    title="报警处理"
    :iconSrc="dashboardTitle"
    width="50vw"
    height="60vh"
    @confirm="submitAlarmHandle"
    @cancel="closeAlarmModal"
  >
    <div class="flex-1 overflow-hidden flex flex-col">
      <!-- 报警详情 -->
      <div class="bg-[#1E2A47]/50 p-[1vw] rounded mb-[1vw]">
        <div class="flex items-center mb-[0.8vw]">
          <div class="w-[0.8vw] h-[0.8vw] rounded-full mr-[0.6vw]" :class="getAlarmLevelClass(selectedAlarm?.level)"></div>
          <div class="text-[1vw] text-white font-medium">{{ selectedAlarm?.location }}</div>
        </div>
        <div class="grid grid-cols-2 gap-[0.8vw]">
          <div class="info-field">
            <div class="text-[0.7vw] text-white">报警类型</div>
            <div class="text-[0.8vw] text-white mt-[0.2vw]">{{ selectedAlarm?.type }}</div>
          </div>
          <div class="info-field">
            <div class="text-[0.7vw] text-white">报警时间</div>
            <div class="text-[0.8vw] text-white mt-[0.2vw]">{{ selectedAlarm?.time }}</div>
          </div>
          <div class="info-field">
            <div class="text-[0.7vw] text-white">报警级别</div>
            <div class="text-[0.8vw] text-white mt-[0.2vw]">{{ getAlarmLevelText(selectedAlarm?.level) }}</div>
          </div>
          <div class="info-field">
            <div class="text-[0.7vw] text-white">处理状态</div>
            <div class="text-[0.8vw] text-white mt-[0.2vw]">{{ selectedAlarm?.status === 'pending' ? '待处理' : '处理中' }}</div>
          </div>
        </div>
      </div>

      <!-- 处理表单 -->
      <div class="flex-1 bg-[#1E2A47]/50 p-[1vw] rounded">
        <div class="text-[0.8vw] text-white mb-[0.8vw]">处理信息</div>
        <div class="space-y-[0.8vw]">
          <div>
            <div class="text-[0.7vw] text-white mb-[0.4vw]">处理方式</div>
            <MiniSelect
              v-model="formState.method"
              :options="[
                { label: '远程处理', value: 'remote' },
                { label: '现场处理', value: 'onsite' },
                { label: '忽略误报', value: 'ignore' },
              ]"
            />
          </div>
          <div>
            <div class="text-[0.7vw] text-white mb-[0.4vw]">处理人员</div>
            <MiniSelect
              v-model="formState.handler"
              :options="[
                { label: '当班值班人员', value: 'duty' },
                { label: '维护人员', value: 'maintenance' },
                { label: '安保人员', value: 'security' },
              ]"
            />
          </div>
          <div>
            <div class="text-[0.7vw] text-white mb-[0.4vw]">处理说明</div>
            <textarea
              v-model="formState.description"
              rows="4"
              placeholder="请输入处理说明..."
              class="w-full bg-[#1E2A47]/50 border border-white/10 text-white text-[0.7vw] rounded p-[0.1vw] resize-none focus:outline-none focus:border-blue-500/50"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </ModalDialog>

  <!-- 智慧安消防弹窗 -->
  <SmartFireSafetyModal
    :visible="smartFireSafetyModalVisible"
    @update:visible="smartFireSafetyModalVisible = $event"
    :icon-src="dashboardTitle"
    :default-fullscreen="true"
  />
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
  import gsap from 'gsap';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import MiniSelect from '@/views/scene/components/MiniSelect.vue';
  import MessageTip from '@/views/scene/components/MessageTip.vue';
  import SmartFireSafetyModal from './SmartFireSafetyModal.vue';

  // 定义props，接收class等属性
  defineProps({
    class: {
      type: String,
      default: '',
    },
  });

  // 类型定义
  interface StatItem {
    label: string;
    value: string; // 显示值，会被动画更新
    unit: string;
    borderColor: string;
    textColor: string;
    targetValue?: number; // 仅用于非派生数据，如系统健康度
    isDerived?: boolean; // 标记是否为派生数据
    getValue?: () => number; // 获取派生数据当前值的函数
  }

  // 报警列表数据
  const alarmList = ref<Array<{ id: number; level: string; location: string; type: string; time: string; status: string }>>([]);

  const generateRecentTimeString = (hoursAgo: number = 0): string => {
    const now = new Date();
    const hourOffset = hoursAgo > 0 ? hoursAgo : 0;
    const minutesOffset = 0;

    const eventTime = new Date(now.getTime() - (hourOffset * 3600 * 1000 + minutesOffset * 60 * 1000));
    return eventTime.toISOString();
  };

  // 修改初始化告警列表的方法
  const initializeAlarmList = () => {
    const initialList = [
      // 2条已处理记录（3小时前）
      {
        id: 3,
        level: 'processed', // 修改为新的级别
        location: '3号配电室温感',
        type: '温度报警',
        time: generateRecentTimeString(3),
        status: 'processing',
      },
      {
        id: 4,
        level: 'processed', // 修改为新的级别
        location: '4号机房UPS',
        type: '设备告警',
        time: generateRecentTimeString(3),
        status: 'processing',
      },
    ];

    alarmList.value = initialList;
  };

  // 报警统计数据
  const alarmStats = ref<StatItem[]>([
    {
      label: '今日报警',
      value: '0',
      unit: '次',
      borderColor: 'border-red-500',
      textColor: 'text-red-500',
      isDerived: true,
      getValue: () => 0, // 固定值2
    },
    {
      label: '待处理',
      value: '0',
      unit: '项',
      borderColor: 'border-yellow-500',
      textColor: 'text-yellow-500',
      isDerived: true,
      getValue: () => 0, // 固定值0
    },
    {
      label: '已处理',
      value: '0',
      unit: '项',
      borderColor: 'border-green-500',
      textColor: 'text-green-500',
      isDerived: true,
      getValue: () => 2, // 固定值2
    },
    {
      label: '系统健康度',
      value: '0',
      unit: '%',
      borderColor: 'border-blue-500',
      textColor: 'text-blue-500',
      targetValue: 99, // 固定值99
      isDerived: false,
    },
  ]);

  // 动画函数 - 更新单个统计项的数值
  const animateStatValue = (statItem: StatItem, newTargetValue: number, delay: number = 0) => {
    const proxy = { animatedValue: parseFloat(statItem.value) || 0 };
    gsap.to(proxy, {
      animatedValue: newTargetValue,
      duration: 1.5, // 动画持续时间
      ease: 'power2.out',
      snap: { animatedValue: 1 }, // 确保数值为整数
      delay,
      onUpdate: () => {
        statItem.value = Math.round(proxy.animatedValue).toString();
      },
    });
  };

  // 滚动相关
  const scrollContainer = ref<HTMLElement | null>(null);
  const scrollContent = ref<HTMLElement | null>(null);
  let scrollTween: gsap.core.Tween | null = null;

  const pauseScroll = () => scrollTween?.pause();
  const playScroll = () => scrollTween?.play();

  const initScrollLogic = () => {
    const container = scrollContainer.value;
    const content = scrollContent.value;

    if (scrollTween) {
      scrollTween.kill();
      scrollTween = null;
    }
    if (container) {
      while (container.children.length > 1) {
        container.removeChild(container.lastChild!);
      }
    }

    if (!container || !content || !content.hasChildNodes() || content.offsetHeight === 0) {
      return;
    }
    if (content.offsetHeight <= container.offsetHeight) {
      return;
    }

    const clone = content.cloneNode(true) as HTMLElement;
    container.appendChild(clone);

    scrollTween = gsap.to([content, clone], {
      y: `-=${content.offsetHeight}`,
      duration: content.offsetHeight / 15,
      ease: 'none',
      repeat: -1,
    });

    container.removeEventListener('mouseenter', pauseScroll);
    container.removeEventListener('mouseleave', playScroll);
    container.addEventListener('mouseenter', pauseScroll);
    container.addEventListener('mouseleave', playScroll);
    scrollTween.play();
  };

  // 处理弹窗相关
  const showAlarmModal = ref(false);
  const smartFireSafetyModalVisible = ref(false);
  const selectedAlarm = ref(null);
  const formState = ref({
    method: 'remote',
    handler: 'duty',
    description: '',
  });

  const messageTip = ref(null);

  // 处理报警的方法
  const handleAlarm = (alarm: any) => {
    selectedAlarm.value = alarm;
    showAlarmModal.value = true;
  };

  // 显示智慧安消防弹窗
  const showSmartFireSafety = () => {
    console.log('Opening smart fire safety panel...');
    smartFireSafetyModalVisible.value = true;
  };

  // 根据报警级别获取对应的样式类
  const getAlarmLevelClass = (level: any) => {
    switch (level) {
      case 'alarm':
        return 'bg-red-500 animate-pulse';
      case 'pending':
        return 'bg-yellow-500';
      case 'processed':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // 格式化时间显示，缩短格式
  const formatTime = (timeStr: string | number | Date) => {
    const date = new Date(timeStr);
    return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // 获取报警级别文本
  const getAlarmLevelText = (level: string | number) => {
    const levelMap: Record<string, string> = {
      alarm: '告警',
      pending: '待处理',
      processed: '已处理',
    };

    return levelMap[String(level)] || '未知状态';
  };

  // 关闭弹窗
  const closeAlarmModal = () => {
    showAlarmModal.value = false;
    selectedAlarm.value = null;
    formState.value = {
      method: 'remote',
      handler: 'duty',
      description: '',
    };
  };

  // 提交处理
  const submitAlarmHandle = async () => {
    if (!formState.value.description) {
      messageTip.value.warning('请填写处理说明');
      return;
    }

    try {
      const index = alarmList.value.findIndex((item) => item.id === selectedAlarm.value.id);
      if (index !== -1) {
        alarmList.value[index].status = 'processing';
      }

      messageTip.value.success('报警处理提交成功');
      closeAlarmModal();
    } catch (error) {
      messageTip.value.error('处理失败，请重试');
    }
  };

  onMounted(() => {
    nextTick(() => {
      // 初始化固定值的动画
      alarmStats.value.forEach((stat, index) => {
        let targetValue = 0;
        switch (stat.label) {
          case '今日报警':
            targetValue = 0;
            break;
          case '待处理':
            targetValue = 0;
            break;
          case '已处理':
            targetValue = 2;
            break;
          case '系统健康度':
            targetValue = 99;
            break;
        }
        animateStatValue(stat, targetValue, index * 0.1);
      });
    });

    // 只初始化一次列表，不再动态更新
    initializeAlarmList();

    // 初始化滚动效果
    nextTick(() => {
      initScrollLogic();
    });
  });

  onBeforeUnmount(() => {
    if (scrollTween) {
      scrollTween.kill();
      scrollTween = null;
    }
    if (scrollContainer.value) {
      scrollContainer.value.removeEventListener('mouseenter', pauseScroll);
      scrollContainer.value.removeEventListener('mouseleave', playScroll);
    }
  });
</script>

<style scoped>
  @keyframes pulse {
    0% {
      opacity: 0.4;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
    100% {
      opacity: 0.4;
      transform: scale(0.8);
    }
  }

  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
</style>
