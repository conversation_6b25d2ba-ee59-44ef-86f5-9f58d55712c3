<template>
  <div :class="['relative', customClass]">
    <ModalDialog v-model:visible="localVisible" title="智慧运维管理" width="1400px" :default-fullscreen="defaultFullscreen" :class="rootClass">
      <div class="h-full flex flex-col gap-[0.8vw]">
        <!-- 功能导航 -->
        <div class="bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
          <div class="flex gap-[1vw]">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'px-[1.2vw] py-[0.6vw] rounded text-[0.7vw] transition-all',
                activeTab === tab.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30 hover:text-white',
              ]"
            >
              <i :class="tab.icon" class="mr-[0.4vw]"></i>
              {{ tab.label }}
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw] overflow-y-auto custom-scrollbar">
          <!-- 停车系统管理 -->
          <ParkingSystemPanel v-if="activeTab === 'parking'" />

          <!-- 运维分类统计 -->
          <OperationsStatsPanel v-if="activeTab === 'stats'" />

          <!-- 节能运维显示 -->
          <EnergySavingPanel v-if="activeTab === 'energy'" />

          <!-- 值班人员信息 -->
          <DutyPersonnelPanel v-if="activeTab === 'duty'" />
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import ParkingSystemPanel from './smart-operations/ParkingSystemPanel.vue';
  import OperationsStatsPanel from './smart-operations/OperationsStatsPanel.vue';
  import EnergySavingPanel from './smart-operations/EnergySavingPanel.vue';
  import DutyPersonnelPanel from './smart-operations/DutyPersonnelPanel.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    iconSrc: {
      type: String,
      default: '',
    },
    defaultFullscreen: {
      type: Boolean,
      default: false,
    },
    class: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:visible']);

  // 将props.class重命名为customClass以避免与HTML class属性冲突
  const customClass = computed(() => props.class);
  const rootClass = computed(() => props.class);

  // 控制弹窗显示
  const localVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 当前激活的标签页
  const activeTab = ref('parking');

  // 标签页配置
  const tabs = ref([
    {
      key: 'parking',
      label: '停车系统管理',
      icon: 'fas fa-car',
    },
    {
      key: 'stats',
      label: '运维分类统计',
      icon: 'fas fa-chart-bar',
    },
    {
      key: 'energy',
      label: '节能运维显示',
      icon: 'fas fa-leaf',
    },
    {
      key: 'duty',
      label: '值班人员信息',
      icon: 'fas fa-users',
    },
  ]);

  // 监听弹窗打开，重置到第一个标签页
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        activeTab.value = 'parking';
      }
    }
  );
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
